/* Additional custom styles for the quiz application */

/* Icon styles for categories */
.fa-ring { color: #f59e0b; }
.fa-heart { color: #ec4899; }
.fa-music { color: #8b5cf6; }
.fa-food { color: #10b981; }
.fa-gift { color: #f43f5e; }
.fa-game { color: #3b82f6; }
.fa-magic { color: #6366f1; }
.fa-dress { color: #d946ef; }
.fa-plane { color: #0ea5e9; }
.fa-trophy { color: #f59e0b; }
.fa-globe { color: #14b8a6; }
.fa-chat { color: #6366f1; }

/* Enhanced refresh button styles */
.action-button.primary.shadow-lg {
    background: linear-gradient(135deg, #0077FF, #00AAFF);
    box-shadow: 0 8px 25px rgba(0, 119, 255, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
    animation: pulse-glow 2s infinite;
}

.action-button.primary.shadow-lg:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 12px 35px rgba(0, 119, 255, 0.6);
    filter: brightness(1.2);
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 8px 25px rgba(0, 119, 255, 0.4);
    }
    50% {
        box-shadow: 0 8px 35px rgba(0, 119, 255, 0.6);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .category-grid {
        grid-template-columns: repeat(2, 1fr);
        grid-template-rows: repeat(4, 1fr);
        gap: 0.5rem;
    }

    .category-card {
        padding: 0.5rem;
        border-width: 4px;
    }

    .category-card h3 {
        font-size: 0.7rem;
        line-height: 1.1;
        margin-top: 0.3rem;
        word-break: break-word;
        hyphens: auto;
    }

    .category-card i {
        font-size: 1.2rem;
    }

    .category-winner {
        padding: 0.25rem;
    }

    .winner-points {
        font-size: 0.9rem;
    }

    .question-text {
        font-size: 1.25rem;
        padding: 1.5rem;
    }

    .answer-button {
        font-size: 1rem;
        padding: 0.75rem;
    }

    /* Player images and logo responsive fixes */
    .player-image-large {
        width: 90px;
        height: 90px;
        object-fit: cover;
    }

    /* Mobile: Logo above players */
    .mobile-layout {
        flex-direction: column;
        align-items: center;
    }

    .mobile-players-container {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-top: 1rem;
    }

    .logo-container {
        flex-shrink: 0;
        margin: 0;
        order: -1; /* Logo comes first in mobile */
    }

    .logo-image {
        height: 140px;
        width: auto;
        max-width: 200px;
        object-fit: contain;
        margin-bottom: 1rem;
    }

    /* Ensure player images in audience pages are square */
    .player-button img {
        width: 70px;
        height: 70px;
        object-fit: cover;
    }

    /* Mobile responsive for refresh buttons */
    .action-button.primary.shadow-lg {
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
        transform: scale(1.0);
    }

    .action-button.primary.shadow-lg:hover {
        transform: scale(1.05) translateY(-1px);
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .category-card h3 {
        font-size: 0.6rem;
    }

    .category-card i {
        font-size: 1rem;
    }

    .category-card {
        padding: 0.4rem;
        border-width: 3px;
    }

    /* Player images responsive fixes */
    .player-image-large {
        width: 80px;
        height: 80px;
        object-fit: cover;
    }

    .logo-container {
        flex-shrink: 0;
        margin: 0 5px;
    }

    .logo-image {
        height: 100px;
        width: auto;
        max-width: 120px;
        object-fit: contain;
    }

    /* Ensure player images in audience pages are square */
    .player-button img {
        width: 60px;
        height: 60px;
        object-fit: cover;
    }

    /* Extra small mobile responsive for refresh buttons */
    .action-button.primary.shadow-lg {
        font-size: 0.9rem;
        padding: 0.6rem 1.2rem;
        transform: scale(1.0);
    }

    .action-button.primary.shadow-lg:hover {
        transform: scale(1.03) translateY(-1px);
    }
}

/* Animation keyframes */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Apply animations */
.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

.slide-up {
    animation: slideUp 0.5s ease forwards;
}

.slide-in {
    animation: slideIn 0.5s ease forwards;
}

/* Confetti effect for correct answers */
.confetti {
    position: fixed;
    width: 10px;
    height: 10px;
    background-color: #f43f5e;
    opacity: 0;
    transform: translateY(0) rotate(0);
    pointer-events: none;
    animation: confetti 3s ease-in-out forwards;
}

@keyframes confetti {
    0% { opacity: 1; transform: translateY(0) rotate(0); }
    100% { opacity: 0; transform: translateY(100vh) rotate(720deg); }
}
